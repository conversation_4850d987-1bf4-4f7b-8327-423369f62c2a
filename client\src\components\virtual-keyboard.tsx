import { Eye, EyeOff } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { getTranslation } from "@/lib/i18n";

interface VirtualKeyboardProps {
  isVisible: boolean;
  onToggleVisibility: () => void;
  currentChar?: string;
  language: string;
  onKeyPress: (key: string) => void;
}

export function VirtualKeyboard({
  isVisible,
  onToggleVisibility,
  currentChar,
  language,
  onKeyPress
}: VirtualKeyboardProps) {
  const keyboardLayout = [
    ['~', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='],
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'],
    ['SPACE']
  ];

  const isKeyActive = (key: string) => {
    if (!currentChar) return false;
    
    if (key === 'SPACE' && currentChar === ' ') return true;
    return key.toLowerCase() === currentChar.toLowerCase();
  };

  if (!isVisible) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-base sm:text-lg font-semibold text-slate-800">{getTranslation(language, 'virtualKeyboard')}</h3>
          <Button variant="ghost" size="sm" onClick={onToggleVisibility} className="min-h-[44px]">
            <Eye className="w-4 h-4 mr-1" />
            <span className="hidden sm:inline">{getTranslation(language, 'show')}</span>
            <span className="sm:hidden">Show</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-4 sm:p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-base sm:text-lg font-semibold text-slate-800">{getTranslation(language, 'virtualKeyboard')}</h3>
        <Button variant="ghost" size="sm" onClick={onToggleVisibility} className="min-h-[44px]">
          <EyeOff className="w-4 h-4 mr-1" />
          <span className="hidden sm:inline">{getTranslation(language, 'hide')}</span>
          <span className="sm:hidden">Hide</span>
        </Button>
      </div>

      <div className="space-y-1 sm:space-y-2 select-none overflow-x-auto">
        {keyboardLayout.map((row, rowIndex) => (
          <div key={rowIndex} className="flex justify-center space-x-1 min-w-max">
            {row.map((key) => (
              <button
                key={key}
                className={`virtual-key ${isKeyActive(key) ? 'active' : ''} ${
                  key === 'SPACE' ? 'w-32 sm:w-48 lg:w-64' : 'min-w-[32px] sm:min-w-[40px]'
                }`}
                onClick={() => onKeyPress(key === 'SPACE' ? ' ' : key.toLowerCase())}
              >
                {key === 'SPACE' ? 'SPACE' : key}
              </button>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}
